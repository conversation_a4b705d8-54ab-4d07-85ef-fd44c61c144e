import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import { FunctionList } from "../../../services/device/devices";
import { LogoutOutlined } from "@ant-design/icons";
import { useState, useEffect } from "react";

const ModalPreventAction = ({
  functionItem,
  handleCancelCalibration,
  setIsCalibProgressFinished,
}: {
  functionItem: FunctionList;
  handleCancelCalibration: () => void;
  setIsCalibProgressFinished: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const [openPopover, setOpenPopover] = useState(false);

  useEffect(() => {
    setIsCalibProgressFinished(false);
  }, []);

  return (
    <div
      style={{
        zIndex: 999,
        height: "calc(100vh - 40px)",
        width: "100vw",
        position: "fixed",
        bottom: 0,
        left: 0,
        backgroundColor: "rgba(0,0,0,0.1)",
        backdropFilter: "blur(1px)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: 8,
          background: "#fff",
          borderRadius: 16,
          padding: 16,
          width: 300,
          height: 150,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <p style={{ fontSize: 16 }}>
          Đang hiệu chuẩn cho Bơm{" "}
          {functionItem.identifier
            ? functionItem.identifier.match(/\d+$/)?.[0] ||
              functionItem.identifier
            : ""}
        </p>

        <div
          style={{
            display: "flex",
            gap: 8,
            flexDirection: "column",
            width: "100%",
          }}
        >
          <Button
            type="primary"
            onClick={() => {
              handleCancelCalibration();
              setIsCalibProgressFinished(true);
            }}
            style={{ backgroundColor: "#52c41a", borderColor: "#52c41a" }}
          >
            ✅ Hoàn thành và nhập kết quả
          </Button>

          <Button
            danger
            type="primary"
            onClick={() => setOpenPopover(true)}
            icon={<LogoutOutlined />}
          >
            ❌ Hủy bỏ hiệu chuẩn
          </Button>
        </div>
        <Popover
          title="Hủy bỏ hiệu chuẩn"
          content={
            <>
              <p>Điều này sẽ hủy bỏ quá trình hiệu chuẩn của bơm</p>
              <p>Bạn có chắc chắn muốn làm điều đó không?</p>
              <Button
                danger
                type="primary"
                onClick={() => handleCancelCalibration()}
                icon={<LogoutOutlined />}
              >
                OK
              </Button>
            </>
          }
          trigger="click"
          open={openPopover}
          onOpenChange={(e) => setOpenPopover(e)}
        ></Popover>
      </div>
    </div>
  );
};

export default ModalPreventAction;
